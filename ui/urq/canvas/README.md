# Canvas 图形绘制组件

基于LVGL 8.4版本实现的Canvas图形绘制组件，支持多种图形的绘制。

## 支持的图形类型

1. **直线 (Line)** - 支持实线、虚线、圆角端点
2. **矩形 (Rectangle)** - 支持填充、边框、圆角
3. **多边形 (Polygon)** - 支持任意多边形的绘制
4. **椭圆 (Ellipse)** - 支持椭圆和圆形
5. **弧形 (Arc)** - 支持弧形绘制
6. **扇形 (Sector)** - 支持扇形绘制
7. **扇环 (Ring)** - 支持环形扇形绘制

## 文件结构

```
ui/urq/canvas/
├── widget.h          # Canvas组件头文件
├── widget.c          # Canvas组件实现
├── test_canvas.h     # 测试函数声明
├── test_canvas.c     # 测试示例
└── README.md         # 说明文档

lib/urq/canvas/
├── type.h            # 图形类型定义
└── conf.h            # 配置结构定义

parse/urq_parse/widget/
└── canvas.hpp        # 配置解析器
```

## 使用方法

### 1. 创建Canvas组件

```c
#include "urq/canvas/widget.h"

// 创建Canvas组件
lv_obj_t *canvas_widget = urq_canvas_widget_create(parent);
```

### 2. 配置Canvas

```c
// 创建配置
urq_widget_conf_t *widget_conf = (urq_widget_conf_t *)urq_malloc(sizeof(urq_widget_conf_t));
urq_widget_conf_init_inplace(widget_conf);
widget_conf->type = URQ_WIDGET_TYPE_CANVAS;

// 创建Canvas配置
urq_canvas_widget_conf_t *canvas_conf = (urq_canvas_widget_conf_t *)urq_malloc(sizeof(urq_canvas_widget_conf_t));
urq_canvas_widget_conf_init_inplace(canvas_conf);

// 设置Canvas基本属性
canvas_conf->canvas_width = 300;
canvas_conf->canvas_height = 200;
canvas_conf->bg_color.rgba.rgba = 0xFFFFFFFF; // 白色背景
canvas_conf->bg_opacity = 255;
```

### 3. 添加图形

```c
// 创建图形数组
canvas_conf->shape_count = 2;
canvas_conf->shapes = (urq_canvas_shape_t *)urq_malloc(sizeof(urq_canvas_shape_t) * canvas_conf->shape_count);

// 添加直线
canvas_conf->shapes[0].type = URQ_CANVAS_SHAPE_LINE;
canvas_conf->shapes[0].shape.line.start = (lv_point_t){10, 10};
canvas_conf->shapes[0].shape.line.end = (lv_point_t){100, 50};
canvas_conf->shapes[0].shape.line.width = 3;
canvas_conf->shapes[0].shape.line.color.rgba.rgba = 0xFF0000FF; // 红色

// 添加矩形
canvas_conf->shapes[1].type = URQ_CANVAS_SHAPE_RECTANGLE;
canvas_conf->shapes[1].shape.rectangle.pos = (lv_point_t){120, 20};
canvas_conf->shapes[1].shape.rectangle.width = 80;
canvas_conf->shapes[1].shape.rectangle.height = 60;
canvas_conf->shapes[1].shape.rectangle.draw_mode = URQ_CANVAS_DRAW_MODE_STROKE_FILL;
canvas_conf->shapes[1].shape.rectangle.fill_color.rgba.rgba = 0x00FF00FF; // 绿色填充
canvas_conf->shapes[1].shape.rectangle.border_color.rgba.rgba = 0x0000FFFF; // 蓝色边框
```

### 4. 应用配置

```c
widget_conf->config.canvas = canvas_conf;
urq_canvas_widget_config(canvas_widget, widget_conf);
```

## 图形配置详解

### 直线 (Line)
- `start`, `end`: 起点和终点坐标
- `width`: 线宽
- `color`: 颜色
- `opacity`: 透明度
- `dash_width`, `dash_gap`: 虚线配置
- `round_start`, `round_end`: 圆角端点

### 矩形 (Rectangle)
- `pos`: 左上角坐标
- `width`, `height`: 宽度和高度
- `draw_mode`: 绘制模式（描边/填充/描边+填充）
- `fill_color`, `fill_opacity`: 填充颜色和透明度
- `border_color`, `border_opacity`, `border_width`: 边框配置
- `radius`: 圆角半径

### 多边形 (Polygon)
- `points`: 顶点数组
- `point_count`: 顶点数量
- `draw_mode`: 绘制模式
- 填充和边框配置同矩形

### 椭圆 (Ellipse)
- `center`: 中心点
- `rx`, `ry`: 水平和垂直半径
- `draw_mode`: 绘制模式
- 填充和边框配置同矩形

### 弧形 (Arc)
- `center`: 中心点
- `radius`: 半径
- `start_angle`, `end_angle`: 起始和结束角度（0-360度）
- `width`: 线宽
- `color`, `opacity`: 颜色和透明度
- `rounded`: 是否圆角

### 扇形 (Sector)
- `center`: 中心点
- `radius`: 半径
- `start_angle`, `end_angle`: 起始和结束角度
- `draw_mode`: 绘制模式
- 填充和边框配置同矩形

### 扇环 (Ring)
- `center`: 中心点
- `inner_radius`, `outer_radius`: 内外半径
- `start_angle`, `end_angle`: 起始和结束角度
- `draw_mode`: 绘制模式
- 填充和边框配置同矩形

## 测试示例

运行测试示例：

```c
#include "urq/canvas/test_canvas.h"

// 在你的页面中创建测试Canvas
lv_obj_t *test_canvas = urq_canvas_create_test_widget(parent);
```

测试示例包含了所有支持的图形类型的演示。

## 注意事项

1. Canvas组件使用LVGL的Canvas对象作为底层实现
2. 内存管理：组件会自动管理Canvas缓冲区的分配和释放
3. 多边形需要至少3个顶点
4. 角度使用度数制（0-360度）
5. 颜色使用RGBA格式
6. 椭圆当前实现为圆形，如需真正椭圆可通过多边形近似实现

## 扩展

如需添加新的图形类型：

1. 在 `lib/urq/canvas/type.h` 中添加新的图形类型枚举
2. 在 `lib/urq/canvas/conf.h` 中添加新的配置结构
3. 在 `ui/urq/canvas/widget.h` 中添加绘制函数声明
4. 在 `ui/urq/canvas/widget.c` 中实现绘制函数
5. 在 `urq_canvas_widget_draw_content` 函数中添加新图形的处理分支
