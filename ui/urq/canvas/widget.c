#include "urq/widget.h"
#include "lvgl.h"
#include "urq/canvas/conf.h"
#include "urq/canvas/widget.h"
#include "urq/log/verbose.h"
#include "urq/page/page.h"
#include "urq/preload.h"
#include "urq/project/project.h"
#include "urq/req/action.h"
#include "urq/user_data.h"
#include "urq/util/color.h"
#include "urq/util/util.h"
#include "urq/widget/property.h"
#include "urq/widget/type.h"
#include <math.h>

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

#define Self urq_canvas_widget_t

static void _constructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj);
static void _destructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj);
static void _event_cb(const lv_obj_class_t *class_p, lv_event_t *e);

const lv_obj_class_t URQ_CANVAS_WIDGET_CLASS = {
    .base_class = &lv_obj_class,
    .constructor_cb = _constructor_cb,
    .destructor_cb = _destructor_cb,
    .user_data = NULL,
    .event_cb = _event_cb,
    .width_def = LV_DPI_DEF,
    .height_def = LV_DPI_DEF,
    .editable = LV_OBJ_CLASS_EDITABLE_INHERIT,
    .group_def = LV_OBJ_CLASS_GROUP_DEF_INHERIT,
    .instance_size = sizeof(Self),
};

static void _constructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj)
{
    urq_used(class_p);
    Self *widget = (Self *)obj;
    urq_user_data_t *user_data = &widget->user_data;

    urq_obj_set_user_data(obj, user_data);
    urq_user_data_init(
        user_data, urq_canvas_widget_config, NULL, urq_canvas_widget_set_state,
        urq_canvas_widget_set_property);

    widget->common_conf = NULL;
    widget->conf = NULL;
    widget->canvas = NULL;
    widget->canvas_buffer = NULL;
}

static void _destructor_cb(const lv_obj_class_t *class_p, lv_obj_t *obj)
{
    urq_used(class_p);
    Self *widget = (Self *)obj;

    if (widget->user_data.type != URQ_WIDGET_TYPE_CLONE) {
        urq_user_data_free(&widget->user_data);

        // 释放Canvas缓冲区
        if (widget->canvas_buffer != NULL) {
            urq_free(widget->canvas_buffer);
            widget->canvas_buffer = NULL;
        }

        if (widget->common_conf != NULL) {
            if (widget->common_conf->styles != NULL) {
                for (size_t idx = 0; idx < widget->common_conf->styles->size;
                     ++idx) {
                    urq_style_t *style = widget->common_conf->styles->data[idx];
                    if (style != NULL && style->font.marquee != NULL) {
                        urq_marquee_disable(style->font.marquee);
                    }
                }
            }
            urq_widget_common_conf_free_inplace(widget->common_conf);
            urq_free(widget->common_conf);
        }
    }
}

static void _event_cb(const lv_obj_class_t *class_p, lv_event_t *e)
{
    if (urq_event_filter_exit(class_p, e)) {
        return;
    }
    Self *widget = (Self *)lv_event_get_target(e);
    urq_widget_common_conf_t *common_conf = widget->common_conf;
    static uint32_t count = 0;

    // 检查是否执行点击事件
    if (urq_event_check_exec_click(e, &count, common_conf)) {
        // exec
        printf("Canvas widget clicked\n");
    }

    if (common_conf != NULL && common_conf->has_action) {
        if (urq_page_add_frame_cb3(
                widget->user_data.page, urq_req_wirte_set_action, urq_noop3,
                (void *)widget->user_data.page,
                (void *)(intptr_t)common_conf->id, (void *)(intptr_t)e->code)) {
            printf("Canvas action added successfully\n");
        }
    }
}

lv_obj_t *urq_canvas_widget_create(lv_obj_t *parent)
{
    lv_obj_t *obj;

    obj = lv_obj_class_create_obj(&URQ_CANVAS_WIDGET_CLASS, parent);
    lv_obj_class_init_obj(obj);
    return obj;
}

void urq_canvas_widget_config(lv_obj_t *self, urq_widget_conf_t *mut_conf)
{
    Self *const widget = (Self *)self;

    widget->common_conf = mut_conf->common_conf;
    widget->conf = mut_conf->config.canvas;

    if (widget->conf != NULL) {
        // 创建Canvas对象
        widget->canvas = lv_canvas_create(self);

        // 分配Canvas缓冲区
        size_t buffer_size = (size_t)widget->conf->canvas_width *
                             (size_t)widget->conf->canvas_height *
                             sizeof(lv_color_t);
        widget->canvas_buffer = (lv_color_t *)urq_malloc(buffer_size);

        if (widget->canvas_buffer != NULL) {
            // 设置Canvas缓冲区
            lv_canvas_set_buffer(
                widget->canvas, widget->canvas_buffer,
                widget->conf->canvas_width, widget->conf->canvas_height,
                LV_IMG_CF_TRUE_COLOR);

            // 设置Canvas大小和位置
            lv_obj_set_size(
                widget->canvas, widget->conf->canvas_width,
                widget->conf->canvas_height);
            lv_obj_center(widget->canvas);

            // 绘制Canvas内容
            urq_canvas_widget_draw_content(widget);
        }
    }

    urq_widget_config(self, mut_conf, NULL);
}

void urq_canvas_widget_draw_content(urq_canvas_widget_t *widget)
{
    if (widget->canvas == NULL || widget->conf == NULL) {
        return;
    }

    urq_theme_color_t *theme_color = widget->user_data.project->env.theme_color;

    // 填充背景
    urq_color_ref_t bg_color_ref = widget->conf->bg_color;
    lv_color_t bg_color = urq_get_color(theme_color, &bg_color_ref);
    lv_opa_t bg_opa = (lv_opa_t)widget->conf->bg_opacity;
    lv_canvas_fill_bg(widget->canvas, bg_color, bg_opa);

    // 绘制所有图形
    for (size_t i = 0; i < widget->conf->shape_count; i++) {
        const urq_canvas_shape_t *shape = &widget->conf->shapes[i];

        switch (shape->type) {
        case URQ_CANVAS_SHAPE_LINE:
            urq_canvas_draw_line_shape(
                widget->canvas, &shape->shape.line, theme_color);
            break;
        case URQ_CANVAS_SHAPE_RECTANGLE:
            urq_canvas_draw_rectangle_shape(
                widget->canvas, &shape->shape.rectangle, theme_color);
            break;
        case URQ_CANVAS_SHAPE_POLYGON:
            urq_canvas_draw_polygon_shape(
                widget->canvas, &shape->shape.polygon, theme_color);
            break;
        case URQ_CANVAS_SHAPE_ELLIPSE:
            urq_canvas_draw_ellipse_shape(
                widget->canvas, &shape->shape.ellipse, theme_color);
            break;
        case URQ_CANVAS_SHAPE_ARC:
            urq_canvas_draw_arc_shape(
                widget->canvas, &shape->shape.arc, theme_color);
            break;
        case URQ_CANVAS_SHAPE_SECTOR:
            urq_canvas_draw_sector_shape(
                widget->canvas, &shape->shape.sector, theme_color);
            break;
        case URQ_CANVAS_SHAPE_RING:
            urq_canvas_draw_ring_shape(
                widget->canvas, &shape->shape.ring, theme_color);
            break;
        default:
            break;
        }
    }
}

void urq_canvas_draw_line_shape(
    lv_obj_t *canvas, const urq_canvas_line_t *line,
    urq_theme_color_t *theme_color)
{
    lv_draw_line_dsc_t line_dsc;
    lv_draw_line_dsc_init(&line_dsc);

    urq_color_ref_t color = line->color;
    line_dsc.color = urq_get_color(theme_color, &color);
    line_dsc.width = line->width;
    line_dsc.opa = (lv_opa_t)line->opacity;
    line_dsc.dash_width = line->dash_width;
    line_dsc.dash_gap = line->dash_gap;
    line_dsc.round_start = line->round_start;
    line_dsc.round_end = line->round_end;

    lv_point_t points[2] = {line->start, line->end};
    lv_canvas_draw_line(canvas, points, 2, &line_dsc);
}

void urq_canvas_draw_rectangle_shape(
    lv_obj_t *canvas, const urq_canvas_rectangle_t *rect,
    urq_theme_color_t *theme_color)
{
    lv_draw_rect_dsc_t rect_dsc;
    lv_draw_rect_dsc_init(&rect_dsc);

    // 设置填充
    if (rect->draw_mode == URQ_CANVAS_DRAW_MODE_FILL ||
        rect->draw_mode == URQ_CANVAS_DRAW_MODE_STROKE_FILL) {
        urq_color_ref_t fill_color = rect->fill_color;
        rect_dsc.bg_color = urq_get_color(theme_color, &fill_color);
        rect_dsc.bg_opa = (lv_opa_t)rect->fill_opacity;
    } else {
        rect_dsc.bg_opa = LV_OPA_TRANSP;
    }

    // 设置边框
    if (rect->draw_mode == URQ_CANVAS_DRAW_MODE_STROKE ||
        rect->draw_mode == URQ_CANVAS_DRAW_MODE_STROKE_FILL) {
        urq_color_ref_t border_color = rect->border_color;
        rect_dsc.border_color = urq_get_color(theme_color, &border_color);
        rect_dsc.border_opa = (lv_opa_t)rect->border_opacity;
        rect_dsc.border_width = rect->border_width;
    } else {
        rect_dsc.border_width = 0;
    }

    rect_dsc.radius = rect->radius;

    lv_canvas_draw_rect(
        canvas, rect->pos.x, rect->pos.y, rect->width, rect->height, &rect_dsc);
}

void urq_canvas_draw_polygon_shape(
    lv_obj_t *canvas, const urq_canvas_polygon_t *polygon,
    urq_theme_color_t *theme_color)
{
    if (polygon->points == NULL || polygon->point_count < 3) {
        return;
    }

    lv_draw_rect_dsc_t rect_dsc;
    lv_draw_rect_dsc_init(&rect_dsc);

    // 设置填充
    if (polygon->draw_mode == URQ_CANVAS_DRAW_MODE_FILL ||
        polygon->draw_mode == URQ_CANVAS_DRAW_MODE_STROKE_FILL) {
        urq_color_ref_t fill_color = polygon->fill_color;
        rect_dsc.bg_color = urq_get_color(theme_color, &fill_color);
        rect_dsc.bg_opa = (lv_opa_t)polygon->fill_opacity;
    } else {
        rect_dsc.bg_opa = LV_OPA_TRANSP;
    }

    // 设置边框
    if (polygon->draw_mode == URQ_CANVAS_DRAW_MODE_STROKE ||
        polygon->draw_mode == URQ_CANVAS_DRAW_MODE_STROKE_FILL) {
        urq_color_ref_t border_color = polygon->border_color;
        rect_dsc.border_color = urq_get_color(theme_color, &border_color);
        rect_dsc.border_opa = (lv_opa_t)polygon->border_opacity;
        rect_dsc.border_width = polygon->border_width;
    } else {
        rect_dsc.border_width = 0;
    }

    lv_canvas_draw_polygon(
        canvas, polygon->points, (uint32_t)polygon->point_count, &rect_dsc);
}

void urq_canvas_draw_ellipse_shape(
    lv_obj_t *canvas, const urq_canvas_ellipse_t *ellipse,
    urq_theme_color_t *theme_color)
{
    // LVGL没有直接的椭圆绘制函数，我们使用多个弧形来近似绘制椭圆
    // 这里简化为圆形处理，如果需要真正的椭圆可以通过数学计算多个点来绘制多边形
    lv_coord_t radius = (ellipse->rx + ellipse->ry) / 2; // 取平均半径

    lv_draw_arc_dsc_t arc_dsc;
    lv_draw_arc_dsc_init(&arc_dsc);

    if (ellipse->draw_mode == URQ_CANVAS_DRAW_MODE_FILL ||
        ellipse->draw_mode == URQ_CANVAS_DRAW_MODE_STROKE_FILL) {
        // 对于填充模式，我们绘制一个填充的圆
        lv_draw_rect_dsc_t rect_dsc;
        lv_draw_rect_dsc_init(&rect_dsc);
        urq_color_ref_t fill_color = ellipse->fill_color;
        rect_dsc.bg_color = urq_get_color(theme_color, &fill_color);
        rect_dsc.bg_opa = (lv_opa_t)ellipse->fill_opacity;
        rect_dsc.radius = radius;

        lv_canvas_draw_rect(
            canvas, ellipse->center.x - radius, ellipse->center.y - radius,
            radius * 2, radius * 2, &rect_dsc);
    }

    if (ellipse->draw_mode == URQ_CANVAS_DRAW_MODE_STROKE ||
        ellipse->draw_mode == URQ_CANVAS_DRAW_MODE_STROKE_FILL) {
        urq_color_ref_t border_color = ellipse->border_color;
        arc_dsc.color = urq_get_color(theme_color, &border_color);
        arc_dsc.width = ellipse->border_width;
        arc_dsc.opa = (lv_opa_t)ellipse->border_opacity;

        lv_canvas_draw_arc(
            canvas, ellipse->center.x, ellipse->center.y, radius, 0, 360,
            &arc_dsc);
    }
}

void urq_canvas_draw_arc_shape(
    lv_obj_t *canvas, const urq_canvas_arc_t *arc,
    urq_theme_color_t *theme_color)
{
    lv_draw_arc_dsc_t arc_dsc;
    lv_draw_arc_dsc_init(&arc_dsc);

    urq_color_ref_t color = arc->color;
    arc_dsc.color = urq_get_color(theme_color, &color);
    arc_dsc.width = arc->width;
    arc_dsc.opa = (lv_opa_t)arc->opacity;
    arc_dsc.rounded = arc->rounded;

    lv_canvas_draw_arc(
        canvas, arc->center.x, arc->center.y, arc->radius, arc->start_angle,
        arc->end_angle, &arc_dsc);
}

void urq_canvas_draw_sector_shape(
    lv_obj_t *canvas, const urq_canvas_sector_t *sector,
    urq_theme_color_t *theme_color)
{
    // 扇形绘制：先绘制填充的弧形区域，然后绘制边框
    if (sector->draw_mode == URQ_CANVAS_DRAW_MODE_FILL ||
        sector->draw_mode == URQ_CANVAS_DRAW_MODE_STROKE_FILL) {

        // 计算扇形的顶点
        uint32_t point_count = 32; // 用32个点来近似扇形
        lv_point_t *points =
            (lv_point_t *)urq_malloc(sizeof(lv_point_t) * (point_count + 2));

        if (points != NULL) {
            // 中心点
            points[0] = sector->center;

            // 计算弧上的点
            float angle_step =
                (float)(sector->end_angle - sector->start_angle) /
                (float)(point_count - 1);
            for (uint32_t i = 0; i < point_count; i++) {
                float angle =
                    ((float)sector->start_angle + (float)i * angle_step) *
                    (float)M_PI / 180.0f;
                points[i + 1].x = sector->center.x +
                                  (lv_coord_t)(sector->radius * cosf(angle));
                points[i + 1].y = sector->center.y +
                                  (lv_coord_t)(sector->radius * sinf(angle));
            }

            lv_draw_rect_dsc_t rect_dsc;
            lv_draw_rect_dsc_init(&rect_dsc);
            urq_color_ref_t fill_color = sector->fill_color;
            rect_dsc.bg_color = urq_get_color(theme_color, &fill_color);
            rect_dsc.bg_opa = (lv_opa_t)sector->fill_opacity;
            rect_dsc.border_width = 0;

            lv_canvas_draw_polygon(canvas, points, point_count + 1, &rect_dsc);
            urq_free(points);
        }
    }

    if (sector->draw_mode == URQ_CANVAS_DRAW_MODE_STROKE ||
        sector->draw_mode == URQ_CANVAS_DRAW_MODE_STROKE_FILL) {

        // 绘制扇形边框：弧形 + 两条半径线
        lv_draw_arc_dsc_t arc_dsc;
        lv_draw_arc_dsc_init(&arc_dsc);
        urq_color_ref_t border_color = sector->border_color;
        arc_dsc.color = urq_get_color(theme_color, &border_color);
        arc_dsc.width = sector->border_width;
        arc_dsc.opa = (lv_opa_t)sector->border_opacity;

        // 绘制弧形
        lv_canvas_draw_arc(
            canvas, sector->center.x, sector->center.y, sector->radius,
            sector->start_angle, sector->end_angle, &arc_dsc);

        // 绘制半径线
        lv_draw_line_dsc_t line_dsc;
        lv_draw_line_dsc_init(&line_dsc);
        urq_color_ref_t line_color = sector->border_color;
        line_dsc.color = urq_get_color(theme_color, &line_color);
        line_dsc.width = sector->border_width;
        line_dsc.opa = (lv_opa_t)sector->border_opacity;

        // 起始半径线
        float start_angle_rad =
            (float)sector->start_angle * (float)M_PI / 180.0f;
        lv_point_t start_line[2] = {
            sector->center,
            {sector->center.x +
                 (lv_coord_t)(sector->radius * cosf(start_angle_rad)),
             sector->center.y +
                 (lv_coord_t)(sector->radius * sinf(start_angle_rad))}};
        lv_canvas_draw_line(canvas, start_line, 2, &line_dsc);

        // 结束半径线
        float end_angle_rad = (float)sector->end_angle * (float)M_PI / 180.0f;
        lv_point_t end_line[2] = {
            sector->center,
            {sector->center.x +
                 (lv_coord_t)(sector->radius * cosf(end_angle_rad)),
             sector->center.y +
                 (lv_coord_t)(sector->radius * sinf(end_angle_rad))}};
        lv_canvas_draw_line(canvas, end_line, 2, &line_dsc);
    }
}

void urq_canvas_draw_ring_shape(
    lv_obj_t *canvas, const urq_canvas_ring_t *ring,
    urq_theme_color_t *theme_color)
{
    // 扇环绘制：绘制外弧减去内弧的区域
    if (ring->draw_mode == URQ_CANVAS_DRAW_MODE_FILL ||
        ring->draw_mode == URQ_CANVAS_DRAW_MODE_STROKE_FILL) {

        // 计算扇环的顶点（外弧 + 内弧）
        uint32_t point_count = 32;                   // 用32个点来近似每个弧
        uint32_t total_points = point_count * 2 + 2; // 外弧 + 内弧 + 连接点
        lv_point_t *points =
            (lv_point_t *)urq_malloc(sizeof(lv_point_t) * total_points);

        if (points != NULL) {
            uint32_t idx = 0;

            // 外弧顶点
            float angle_step = (float)(ring->end_angle - ring->start_angle) /
                               (float)(point_count - 1);
            for (uint32_t i = 0; i < point_count; i++) {
                float angle =
                    ((float)ring->start_angle + (float)i * angle_step) *
                    (float)M_PI / 180.0f;
                points[idx].x = ring->center.x +
                                (lv_coord_t)(ring->outer_radius * cosf(angle));
                points[idx].y = ring->center.y +
                                (lv_coord_t)(ring->outer_radius * sinf(angle));
                idx++;
            }

            // 内弧顶点（逆序）
            for (uint32_t i = 0; i < point_count; i++) {
                float angle = ((float)ring->end_angle - (float)i * angle_step) *
                              (float)M_PI / 180.0f;
                points[idx].x = ring->center.x +
                                (lv_coord_t)(ring->inner_radius * cosf(angle));
                points[idx].y = ring->center.y +
                                (lv_coord_t)(ring->inner_radius * sinf(angle));
                idx++;
            }

            lv_draw_rect_dsc_t rect_dsc;
            lv_draw_rect_dsc_init(&rect_dsc);
            urq_color_ref_t fill_color = ring->fill_color;
            rect_dsc.bg_color = urq_get_color(theme_color, &fill_color);
            rect_dsc.bg_opa = (lv_opa_t)ring->fill_opacity;
            rect_dsc.border_width = 0;

            lv_canvas_draw_polygon(canvas, points, point_count * 2, &rect_dsc);
            urq_free(points);
        }
    }

    if (ring->draw_mode == URQ_CANVAS_DRAW_MODE_STROKE ||
        ring->draw_mode == URQ_CANVAS_DRAW_MODE_STROKE_FILL) {

        // 绘制扇环边框：外弧 + 内弧 + 两条半径线
        lv_draw_arc_dsc_t arc_dsc;
        lv_draw_arc_dsc_init(&arc_dsc);
        urq_color_ref_t border_color = ring->border_color;
        arc_dsc.color = urq_get_color(theme_color, &border_color);
        arc_dsc.width = ring->border_width;
        arc_dsc.opa = (lv_opa_t)ring->border_opacity;

        // 绘制外弧
        lv_canvas_draw_arc(
            canvas, ring->center.x, ring->center.y, ring->outer_radius,
            ring->start_angle, ring->end_angle, &arc_dsc);

        // 绘制内弧
        lv_canvas_draw_arc(
            canvas, ring->center.x, ring->center.y, ring->inner_radius,
            ring->start_angle, ring->end_angle, &arc_dsc);

        // 绘制连接线
        lv_draw_line_dsc_t line_dsc;
        lv_draw_line_dsc_init(&line_dsc);
        urq_color_ref_t line_color = ring->border_color;
        line_dsc.color = urq_get_color(theme_color, &line_color);
        line_dsc.width = ring->border_width;
        line_dsc.opa = (lv_opa_t)ring->border_opacity;

        // 起始连接线
        float start_angle_rad = (float)ring->start_angle * (float)M_PI / 180.0f;
        lv_point_t start_line[2] = {
            {ring->center.x +
                 (lv_coord_t)(ring->inner_radius * cosf(start_angle_rad)),
             ring->center.y +
                 (lv_coord_t)(ring->inner_radius * sinf(start_angle_rad))},
            {ring->center.x +
                 (lv_coord_t)(ring->outer_radius * cosf(start_angle_rad)),
             ring->center.y +
                 (lv_coord_t)(ring->outer_radius * sinf(start_angle_rad))}};
        lv_canvas_draw_line(canvas, start_line, 2, &line_dsc);

        // 结束连接线
        float end_angle_rad = (float)ring->end_angle * (float)M_PI / 180.0f;
        lv_point_t end_line[2] = {
            {ring->center.x +
                 (lv_coord_t)(ring->inner_radius * cosf(end_angle_rad)),
             ring->center.y +
                 (lv_coord_t)(ring->inner_radius * sinf(end_angle_rad))},
            {ring->center.x +
                 (lv_coord_t)(ring->outer_radius * cosf(end_angle_rad)),
             ring->center.y +
                 (lv_coord_t)(ring->outer_radius * sinf(end_angle_rad))}};
        lv_canvas_draw_line(canvas, end_line, 2, &line_dsc);
    }
}

void urq_canvas_widget_set_property_callback(
    lv_obj_t *self, uint8_t property_id, uint8_t index, uint16_t value)
{
    urq_used(self);
    urq_used(index);
    urq_used(value);
    switch (property_id) {
    // case URQ_PROPERTY_ID_CANVAS_REDRAW:
    //     urq_canvas_widget_draw_content((urq_canvas_widget_t *)self);
    //     break;
    default:
        break;
    }
}

void urq_canvas_widget_set_property(
    lv_obj_t *self, uint8_t property_id, uint8_t index, uint16_t value)
{
    urq_canvas_widget_t *const widget = (urq_canvas_widget_t *)self;
    urq_widget_set_property(
        self, widget->common_conf, property_id, index, value,
        urq_canvas_widget_set_property_callback);
}

void urq_canvas_widget_set_state(
    lv_obj_t *self, urq_state_t state, uint8_t platfrom)
{
    Self *const widget = (Self *)self;
    urq_set_state(self, widget->common_conf, state, platfrom);
}
