#pragma once

#include "lvgl.h"
#include "urq/canvas/conf.h"
#include "urq/user_data.h"

#ifndef URQ__CANVAS__WIDGET_H
#define URQ__CANVAS__WIDGET_H

#ifdef __cplusplus
extern "C" {
#endif

typedef struct {
    /// @brief 父组件
    lv_obj_t super;
    /// @brief user data 字段
    urq_user_data_t user_data;
    /// @brief 组件配置
    urq_canvas_widget_conf_t *conf;
    /// @brief 通用配置
    urq_widget_common_conf_t *common_conf;
    /// @brief Canvas对象
    lv_obj_t *canvas;
    /// @brief Canvas缓冲区
    lv_color_t *canvas_buffer;
} urq_canvas_widget_t;

/// @brief 创建 canvas_widget
/// @param parent 当前组件所属的页面
/// @returns 组件
lv_obj_t *urq_canvas_widget_create(lv_obj_t *parent)
    __attribute__((__nonnull__(1)));

/// @brief 配置 canvas_widget
/// @param self     组件自身
/// @param mut_conf 配置的内容
/// @returns 组件
void urq_canvas_widget_config(lv_obj_t *self, urq_widget_conf_t *mut_conf)
    __attribute__((__nonnull__(1, 2)));

/// @brief 设置 canvas_widget 的属性
/// @param self     组件自身
/// @param property_id 属性ID
/// @param index 索引
/// @param value 值
void urq_canvas_widget_set_property(
    lv_obj_t *self, uint8_t property_id, uint8_t index, uint16_t value)
    __attribute__((__nonnull__(1)));

/// @brief 设置 canvas_widget 的状态
/// @param self 组件自身
/// @param state 状态
void urq_canvas_widget_set_state(
    lv_obj_t *self, urq_state_t state, uint8_t platfrom)
    __attribute__((__nonnull__(1)));

/// @brief 绘制Canvas内容
/// @param widget Canvas组件
void urq_canvas_widget_draw_content(urq_canvas_widget_t *widget)
    __attribute__((__nonnull__(1)));

/// @brief 绘制直线
/// @param canvas Canvas对象
/// @param line 直线配置
/// @param theme_color 主题颜色
void urq_canvas_draw_line_shape(
    lv_obj_t *canvas, const urq_canvas_line_t *line, 
    urq_theme_color_t *theme_color) __attribute__((__nonnull__(1, 2)));

/// @brief 绘制矩形
/// @param canvas Canvas对象
/// @param rect 矩形配置
/// @param theme_color 主题颜色
void urq_canvas_draw_rectangle_shape(
    lv_obj_t *canvas, const urq_canvas_rectangle_t *rect,
    urq_theme_color_t *theme_color) __attribute__((__nonnull__(1, 2)));

/// @brief 绘制多边形
/// @param canvas Canvas对象
/// @param polygon 多边形配置
/// @param theme_color 主题颜色
void urq_canvas_draw_polygon_shape(
    lv_obj_t *canvas, const urq_canvas_polygon_t *polygon,
    urq_theme_color_t *theme_color) __attribute__((__nonnull__(1, 2)));

/// @brief 绘制椭圆
/// @param canvas Canvas对象
/// @param ellipse 椭圆配置
/// @param theme_color 主题颜色
void urq_canvas_draw_ellipse_shape(
    lv_obj_t *canvas, const urq_canvas_ellipse_t *ellipse,
    urq_theme_color_t *theme_color) __attribute__((__nonnull__(1, 2)));

/// @brief 绘制弧形
/// @param canvas Canvas对象
/// @param arc 弧形配置
/// @param theme_color 主题颜色
void urq_canvas_draw_arc_shape(
    lv_obj_t *canvas, const urq_canvas_arc_t *arc,
    urq_theme_color_t *theme_color) __attribute__((__nonnull__(1, 2)));

/// @brief 绘制扇形
/// @param canvas Canvas对象
/// @param sector 扇形配置
/// @param theme_color 主题颜色
void urq_canvas_draw_sector_shape(
    lv_obj_t *canvas, const urq_canvas_sector_t *sector,
    urq_theme_color_t *theme_color) __attribute__((__nonnull__(1, 2)));

/// @brief 绘制扇环
/// @param canvas Canvas对象
/// @param ring 扇环配置
/// @param theme_color 主题颜色
void urq_canvas_draw_ring_shape(
    lv_obj_t *canvas, const urq_canvas_ring_t *ring,
    urq_theme_color_t *theme_color) __attribute__((__nonnull__(1, 2)));

#ifdef __cplusplus
}
#endif

#endif // URQ__CANVAS__WIDGET_H
