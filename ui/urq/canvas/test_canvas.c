#include "urq/canvas/widget.h"
#include "urq/canvas/conf.h"
#include "urq/widget/conf.h"
#include "urq/project/project.h"
#include "lvgl.h"

/// @brief 创建一个测试Canvas组件
/// @param parent 父对象
/// @return Canvas组件
lv_obj_t *urq_canvas_create_test_widget(lv_obj_t *parent)
{
    // 创建Canvas组件
    lv_obj_t *canvas_widget = urq_canvas_widget_create(parent);
    
    // 创建配置
    urq_widget_conf_t *widget_conf = (urq_widget_conf_t *)urq_malloc(sizeof(urq_widget_conf_t));
    urq_widget_conf_init_inplace(widget_conf);
    
    // 设置组件类型
    widget_conf->type = URQ_WIDGET_TYPE_CANVAS;
    
    // 创建Canvas配置
    urq_canvas_widget_conf_t *canvas_conf = (urq_canvas_widget_conf_t *)urq_malloc(sizeof(urq_canvas_widget_conf_t));
    urq_canvas_widget_conf_init_inplace(canvas_conf);
    
    // 设置Canvas基本属性
    canvas_conf->canvas_width = 300;
    canvas_conf->canvas_height = 200;
    canvas_conf->bg_color.rgba.rgba = 0xFFFFFFFF; // 白色背景
    canvas_conf->bg_opacity = 255;
    
    // 创建测试图形
    canvas_conf->shape_count = 5;
    canvas_conf->shapes = (urq_canvas_shape_t *)urq_malloc(sizeof(urq_canvas_shape_t) * canvas_conf->shape_count);
    
    // 图形1: 直线
    canvas_conf->shapes[0].type = URQ_CANVAS_SHAPE_LINE;
    canvas_conf->shapes[0].shape.line.start = (lv_point_t){10, 10};
    canvas_conf->shapes[0].shape.line.end = (lv_point_t){100, 50};
    canvas_conf->shapes[0].shape.line.width = 3;
    canvas_conf->shapes[0].shape.line.color.rgba.rgba = 0xFF0000FF; // 红色
    canvas_conf->shapes[0].shape.line.opacity = 255;
    canvas_conf->shapes[0].shape.line.dash_width = 0;
    canvas_conf->shapes[0].shape.line.dash_gap = 0;
    canvas_conf->shapes[0].shape.line.round_start = false;
    canvas_conf->shapes[0].shape.line.round_end = false;
    
    // 图形2: 矩形
    canvas_conf->shapes[1].type = URQ_CANVAS_SHAPE_RECTANGLE;
    canvas_conf->shapes[1].shape.rectangle.pos = (lv_point_t){120, 20};
    canvas_conf->shapes[1].shape.rectangle.width = 80;
    canvas_conf->shapes[1].shape.rectangle.height = 60;
    canvas_conf->shapes[1].shape.rectangle.draw_mode = URQ_CANVAS_DRAW_MODE_STROKE_FILL;
    canvas_conf->shapes[1].shape.rectangle.fill_color.rgba.rgba = 0x00FF00FF; // 绿色填充
    canvas_conf->shapes[1].shape.rectangle.fill_opacity = 128;
    canvas_conf->shapes[1].shape.rectangle.border_color.rgba.rgba = 0x0000FFFF; // 蓝色边框
    canvas_conf->shapes[1].shape.rectangle.border_opacity = 255;
    canvas_conf->shapes[1].shape.rectangle.border_width = 2;
    canvas_conf->shapes[1].shape.rectangle.radius = 10;
    
    // 图形3: 圆形（椭圆）
    canvas_conf->shapes[2].type = URQ_CANVAS_SHAPE_ELLIPSE;
    canvas_conf->shapes[2].shape.ellipse.center = (lv_point_t){80, 120};
    canvas_conf->shapes[2].shape.ellipse.rx = 40;
    canvas_conf->shapes[2].shape.ellipse.ry = 40;
    canvas_conf->shapes[2].shape.ellipse.draw_mode = URQ_CANVAS_DRAW_MODE_FILL;
    canvas_conf->shapes[2].shape.ellipse.fill_color.rgba.rgba = 0xFFFF00FF; // 黄色
    canvas_conf->shapes[2].shape.ellipse.fill_opacity = 200;
    
    // 图形4: 弧形
    canvas_conf->shapes[3].type = URQ_CANVAS_SHAPE_ARC;
    canvas_conf->shapes[3].shape.arc.center = (lv_point_t){200, 120};
    canvas_conf->shapes[3].shape.arc.radius = 30;
    canvas_conf->shapes[3].shape.arc.start_angle = 45;
    canvas_conf->shapes[3].shape.arc.end_angle = 315;
    canvas_conf->shapes[3].shape.arc.width = 5;
    canvas_conf->shapes[3].shape.arc.color.rgba.rgba = 0xFF00FFFF; // 紫色
    canvas_conf->shapes[3].shape.arc.opacity = 255;
    canvas_conf->shapes[3].shape.arc.rounded = true;
    
    // 图形5: 扇形
    canvas_conf->shapes[4].type = URQ_CANVAS_SHAPE_SECTOR;
    canvas_conf->shapes[4].shape.sector.center = (lv_point_t){250, 50};
    canvas_conf->shapes[4].shape.sector.radius = 35;
    canvas_conf->shapes[4].shape.sector.start_angle = 0;
    canvas_conf->shapes[4].shape.sector.end_angle = 120;
    canvas_conf->shapes[4].shape.sector.draw_mode = URQ_CANVAS_DRAW_MODE_STROKE_FILL;
    canvas_conf->shapes[4].shape.sector.fill_color.rgba.rgba = 0x00FFFFFF; // 青色填充
    canvas_conf->shapes[4].shape.sector.fill_opacity = 150;
    canvas_conf->shapes[4].shape.sector.border_color.rgba.rgba = 0x000000FF; // 黑色边框
    canvas_conf->shapes[4].shape.sector.border_opacity = 255;
    canvas_conf->shapes[4].shape.sector.border_width = 2;
    
    // 设置配置
    widget_conf->config.canvas = canvas_conf;
    
    // 配置组件
    urq_canvas_widget_config(canvas_widget, widget_conf);
    
    return canvas_widget;
}
