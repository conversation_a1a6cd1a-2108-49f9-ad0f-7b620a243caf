#include "urq/widget.h"
#include "urq/bit/widget.h"
#include "urq/button/widget.h"
#include "urq/calendar/widget.h"
#include "urq/canvas/widget.h"
#include "urq/clone/widget.h"
#include "urq/curve/bar/widget.h"
#include "urq/curve/scatter/widget.h"
#include "urq/curve/trend/widget.h"
#include "urq/curve/trend_curve.h"
#include "urq/curve/xy/widget.h"
#include "urq/curve/xy_curve.h"
#include "urq/dropList/widget.h"
#include "urq/font.h"
#include "urq/group/widget.h"
#include "urq/image/widget.h"
#include "urq/keyboard/widget.h"
#include "urq/label/widget.h"
#include "urq/log/debug.h"
#include "urq/meter/widget.h"
#include "urq/number/widget.h"
#include "urq/rollerList/widget.h"
#include "urq/ruler/widget.h"
#include "urq/string/widget.h"
#include "urq/util/control.h"
#include "urq/util/font.h"
#include "urq/util/state.h"
#include "urq/widget/property.h"
#include "urq/widget/type.h"
#include "urq/word/widget.h"

typedef lv_obj_t *(*_factory_t)(lv_obj_t *parent);

/// @brief 工厂表
static const _factory_t URQ_FACTORYS[URQ_WIDGET_TYPE_MAX + 1] = {
    [URQ_WIDGET_TYPE_STRING] = urq_string_widget_create,
    [URQ_WIDGET_TYPE_NUMBER] = urq_number_widget_create,
    [URQ_WIDGET_TYPE_LABEL_WIDGET] = urq_label_widget_create,
    [URQ_WIDGET_TYPE_IMAGE_WIDGET] = urq_image_widget_create,
    [URQ_WIDGET_TYPE_BIT] = urq_bit_widget_create,
    [URQ_WIDGET_TYPE_WORD] = urq_word_widget_create,
    [URQ_WIDGET_TYPE_KEYBOARD] = urq_keyboard_widget_create,
    [URQ_WIDGET_TYPE_BUTTON] = urq_button_widget_create,
    [URQ_WIDGET_TYPE_CLONE] = urq_clone_widget_create,
    [URQ_WIDGET_TYPE_TREND_CURVE] = urq_trend_curve_widget_create,
    [URQ_WIDGET_TYPE_XY_CURVE] = urq_xy_curve_widget_create,
    [URQ_WIDGET_TYPE_BAR_CURVE] = urq_bar_curve_widget_create,
    [URQ_WIDGET_TYPE_SCATTER_CURVE] = urq_scatter_curve_widget_create,
    [URQ_WIDGET_TYPE_CALENDAR] = urq_calendar_widget_create,
    [URQ_WIDGET_TYPE_ROLLER_LIST] = urq_roller_list_widget_create,
    [URQ_WIDGET_TYPE_DROP_LIST] = urq_drop_list_widget_create,
    [URQ_WIDGET_TYPE_GROUP] = urq_group_widget_create,
    [URQ_WIDGET_TYPE_METER] = urq_meter_widget_create,
    [URQ_WIDGET_TYPE_RULER] = urq_ruler_widget_create,
    [URQ_WIDGET_TYPE_CANVAS] = urq_canvas_widget_create,
};

lv_obj_t *urq_widget_get(
    urq_page_t *page, lv_obj_t *parent, urq_widget_conf_t *conf)
{
    if (urq_lvgl_map_has(page->widget_map, (uint32_t)conf->id)) {
        lv_obj_t *obj = urq_lvgl_map_get(page->widget_map, (uint32_t)conf->id);
        if (obj != NULL) {
            return obj;
        }
    }

    lv_obj_t *obj = urq_widget_get_no_cache(page, parent, conf);

    if (obj != NULL) {
        urq_lvgl_map_add(page->widget_map, (uint32_t)conf->id, obj);
    }

    return obj;
}

lv_obj_t *urq_widget_get_no_cache(
    urq_page_t *page, lv_obj_t *parent, urq_widget_conf_t *conf)
{
    lv_obj_t *obj = urq_widget_create(parent, conf->type);
    if (obj == NULL) {
        return NULL;
    }

    urq_user_data_t *user_data = urq_obj_get_user_data(obj);
    user_data->type = conf->type;
    user_data->id = conf->id;
    user_data->project = page->project;
    user_data->page = page;
    user_data->conf(obj, conf);
    return obj;
}

/// @brief 创建 widget
/// @param parent 所属页面
/// @returns 组件
lv_obj_t *urq_widget_create(lv_obj_t *parent, urq_widget_type_t type)
{
    if (type < 0 || type > URQ_WIDGET_TYPE_MAX) {
        log_e("create widget, invalid type: %d\n", type);
        return NULL;
    }

    _factory_t factory = URQ_FACTORYS[type];
    if (factory == NULL) {
        log_e(
            "widget factory is null: %s(%d)\n", urq_widget_type_to_string(type),
            type);
        return NULL;
    }

    log_v("create widget: (%s)%d\n", urq_widget_type_to_string(type), type);
    return factory(parent);
}

void urq_widget_config(
    lv_obj_t *self, urq_widget_conf_t *conf, urq_widget_conf_fn_t fn)
{
    urq_user_data_t *udata = urq_obj_get_user_data(self);
    urq_project_t *const project = udata->project;

    if (udata->type != URQ_WIDGET_TYPE_CLONE) {
        lv_obj_set_pos(self, conf->pos.x, conf->pos.y);
        lv_obj_set_size(self, conf->size.w, conf->size.h);
    }
    lv_obj_add_flag(self, LV_OBJ_FLAG_CLICKABLE);

    if (conf->common_conf->styles != NULL &&
        conf->common_conf->styles->size > 0) {
        udata->label = lv_label_create(self);
        lv_label_set_text(udata->label, "");
        lv_obj_set_align(udata->label, LV_ALIGN_CENTER);

        for (size_t idx = 0; idx < conf->common_conf->styles->size; ++idx) {
            urq_style_t *style = conf->common_conf->styles->data[idx];
            if (style != NULL && style->font.marquee != NULL) {
                // 设置跑马灯
                style->font.marquee->start_pos = conf->size.w;
                style->font.marquee->end_pos = conf->size.h;
            }
        }
    }

    if (conf->common_conf->graphics != NULL &&
        conf->common_conf->graphics->size > 0) {
        udata->img = lv_img_create(self);
        lv_obj_set_align(udata->img, LV_ALIGN_CENTER);
    }

    if (fn != NULL) {
        fn(self, conf);
    }

    // 配置控制条件
    urq_control_disable_widget(conf->common_conf->control, self);
    /// 设置状态
    if (udata->set_state != NULL)
        udata->set_state(self, project->env.var_sys.state, 1);

    conf->type = URQ_WIDGET_TYPE_NONE;
    conf->config.pointer = NULL;
    conf->common_conf = NULL;
}

void urq_widget_set_property(
    lv_obj_t *self, urq_widget_common_conf_t *common_conf, uint8_t property_id,
    uint8_t index, uint16_t value, urq_widget_set_property_fn_t fn)
{
    // 组合ID 查找atag
    // urq_atag_t *atag = urq_widget_property_map_get(
    //     page->widget_property_map, (uint32_t)widget->common_conf->id);
    // if (atag == NULL) {
    //     log_e("atag is NULL\n");
    // }

    if (property_id < 150) {
        // 公共属性
        urq_deal_property(self, common_conf, property_id, index, value);
    } else {
        if (fn != NULL) {
            fn(self, property_id, index, value);
        }
    }
}
