#pragma once

#include "urq/color.h"
#include "urq/curve/scale.h"
#include "urq/font.h"
#include "urq/ruler/type.h"
#include "urq/style/line.h"
#include "urq/style/point.h"
#include <lvgl/src/misc/lv_area.h>

#ifndef URQ__RULER__CONF_H
#define URQ__RULER__CONF_H

#ifdef __cplusplus
extern "C" {
#endif

/// @brief 刻度尺配置
typedef struct {
    /// @brief 刻度尺类型
    urq_ruler_type_t type;
    /// @brief 刻度对齐方式
    urq_ruler_alignment_t alignment;
    /// @brief 基础刻度配置
    urq_curve_scale_t scale;
    /// @brief 刻度尺主线样式
    urq_style_line_t *main_line;
    /// @brief 点
    lv_point_t *point;
    /// @brief 点数量
    uint8_t point_cnt;
    /// @brief 弧形刻度尺专用：半径
    lv_coord_t radius;
} urq_ruler_widget_conf_t;

/// @brief 初始化刻度尺配置
/// @param self 配置结构指针
static inline void urq_ruler_widget_conf_init_inplace(
    urq_ruler_widget_conf_t *self) __attribute__((__nonnull__(1)));

/// @brief 释放刻度尺配置
/// @param self 配置结构指针
static inline void urq_ruler_widget_conf_free_inplace(
    urq_ruler_widget_conf_t *self) __attribute__((__nonnull__(1)));

void urq_ruler_widget_conf_init_inplace(urq_ruler_widget_conf_t *self)
{
    self->type = URQ_RULER_TYPE_HORIZONTAL;
    self->alignment = URQ_RULER_ALIGNMENT_CENTER;
    urq_curve_scale_init_inplace(&self->scale);
    self->main_line = NULL;
    self->radius = 50;
}

void urq_ruler_widget_conf_free_inplace(urq_ruler_widget_conf_t *self)
{
    urq_curve_scale_free_inplace(&self->scale);

    if (self->main_line != NULL) {
        urq_style_line_free_inplace(self->main_line);
        urq_free(self->main_line);
        self->main_line = NULL;
    }
}

#ifdef __cplusplus
}
#endif

#endif // URQ__RULER__CONF_H
