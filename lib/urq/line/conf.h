#pragma once

#include "urq/bit/conf.h"
#include "urq/style/line.h"
#include <stdint.h>

#ifndef URQ__LINE__CONF_H
#define URQ__LINE__CONF_H
#ifdef __cplusplus
extern "C" {
#endif

typedef enum {
    /// @brief 未指定
    URQ_LINE_DIRECTION_UNSPECIFIED,
    /// @brief 水平
    URQ_LINE_DIRECTION_HORIZONTAL,
    /// @brief 垂直
    URQ_LINE_DIRECTION_VERTICAL,
} urq_line_direction_t;

typedef struct {
    /// @brief 线条颜色
    urq_style_line_t line;
    /// @brief 线条方向
    urq_line_direction_t direction;
    /// @brief 线条顶点
    lv_point_t *points;
    /// @brief 线条顶点数量
    size_t points_size;
    /// @brief 箭头样式
    // urq_style_arrow_t arrow;
} urq_line_widget_conf_t;

static inline void urq_line_widget_conf_init_inplace(
    urq_line_widget_conf_t *self) __attribute__((__nonnull__(1)));

static inline void urq_line_widget_conf_free_inplace(
    urq_line_widget_conf_t *self) __attribute__((__nonnull__(1)));

// ================= impl =============================

void urq_line_widget_conf_init_inplace(urq_line_widget_conf_t *self)
{
    urq_style_line_init_inplace(&self->line);
    self->direction = URQ_LINE_DIRECTION_UNSPECIFIED;
    self->points = NULL;
    self->points_size = 0;
}

void urq_line_widget_conf_free_inplace(urq_line_widget_conf_t *self)
{
    if (self->points != NULL) {
        urq_free(self->points);
    }
}

#ifdef __cplusplus
}
#endif
#endif // URQ__BIT__CONF_H
