#pragma once

#include "urq/style/line.h"
#include <stdint.h>

#ifndef URQ__LINE__PARAM_H
#define URQ__LINE__PARAM_H
#ifdef __cplusplus
extern "C" {
#endif

typedef struct {
    /// @brief 线条长度
    float len;
    /// @brief x轴长度
    float x_len;
    /// @brief y轴长度
    float y_len;
    /// @brief x向量
    float x_vector;
    /// @brief y向量
    float y_vector;
} urq_line_param_t;

#ifdef __cplusplus
}
#endif
#endif // URQ__LINE__PARAM_H
