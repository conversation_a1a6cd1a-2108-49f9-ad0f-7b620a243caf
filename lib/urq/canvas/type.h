#pragma once

#include <stdint.h>

#ifndef URQ__CANVAS__TYPE_H
#define URQ__CANVAS__TYPE_H

#ifdef __cplusplus
extern "C" {
#endif

/// @brief Canvas图形类型
typedef enum {
    /// @brief 直线
    URQ_CANVAS_SHAPE_LINE = 0,
    /// @brief 矩形
    URQ_CANVAS_SHAPE_RECTANGLE = 1,
    /// @brief 多边形
    URQ_CANVAS_SHAPE_POLYGON = 2,
    /// @brief 椭圆
    URQ_CANVAS_SHAPE_ELLIPSE = 3,
    /// @brief 弧形
    URQ_CANVAS_SHAPE_ARC = 4,
    /// @brief 扇形
    URQ_CANVAS_SHAPE_SECTOR = 5,
    /// @brief 扇环
    URQ_CANVAS_SHAPE_RING = 6,
    /// @brief 最大值
    URQ_CANVAS_SHAPE_MAX
} urq_canvas_shape_type_t;

/// @brief Canvas绘制模式
typedef enum {
    /// @brief 描边
    URQ_CANVAS_DRAW_MODE_STROKE = 0,
    /// @brief 填充
    URQ_CANVAS_DRAW_MODE_FILL = 1,
    /// @brief 描边+填充
    URQ_CANVAS_DRAW_MODE_STROKE_FILL = 2
} urq_canvas_draw_mode_t;

#ifdef __cplusplus
}
#endif

#endif // URQ__CANVAS__TYPE_H
