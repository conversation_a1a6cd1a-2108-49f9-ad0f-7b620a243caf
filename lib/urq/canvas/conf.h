#pragma once

#include "urq/canvas/type.h"
#include "urq/color.h"
#include "urq/preload.h"
#include "lvgl.h"
#include <stdint.h>

#ifndef URQ__CANVAS__CONF_H
#define URQ__CANVAS__CONF_H

#ifdef __cplusplus
extern "C" {
#endif

/// @brief 直线配置
typedef struct {
    /// @brief 起点
    lv_point_t start;
    /// @brief 终点
    lv_point_t end;
    /// @brief 线宽
    uint8_t width;
    /// @brief 颜色
    urq_color_ref_t color;
    /// @brief 透明度
    uint8_t opacity;
    /// @brief 虚线宽度
    uint8_t dash_width;
    /// @brief 虚线间隔
    uint8_t dash_gap;
    /// @brief 圆角起点
    bool round_start;
    /// @brief 圆角终点
    bool round_end;
} urq_canvas_line_t;

/// @brief 矩形配置
typedef struct {
    /// @brief 左上角坐标
    lv_point_t pos;
    /// @brief 宽度
    lv_coord_t width;
    /// @brief 高度
    lv_coord_t height;
    /// @brief 绘制模式
    urq_canvas_draw_mode_t draw_mode;
    /// @brief 填充颜色
    urq_color_ref_t fill_color;
    /// @brief 填充透明度
    uint8_t fill_opacity;
    /// @brief 边框颜色
    urq_color_ref_t border_color;
    /// @brief 边框透明度
    uint8_t border_opacity;
    /// @brief 边框宽度
    uint8_t border_width;
    /// @brief 圆角半径
    lv_coord_t radius;
} urq_canvas_rectangle_t;

/// @brief 多边形配置
typedef struct {
    /// @brief 顶点数组
    lv_point_t *points;
    /// @brief 顶点数量
    size_t point_count;
    /// @brief 绘制模式
    urq_canvas_draw_mode_t draw_mode;
    /// @brief 填充颜色
    urq_color_ref_t fill_color;
    /// @brief 填充透明度
    uint8_t fill_opacity;
    /// @brief 边框颜色
    urq_color_ref_t border_color;
    /// @brief 边框透明度
    uint8_t border_opacity;
    /// @brief 边框宽度
    uint8_t border_width;
} urq_canvas_polygon_t;

/// @brief 椭圆配置
typedef struct {
    /// @brief 中心点
    lv_point_t center;
    /// @brief 水平半径
    lv_coord_t rx;
    /// @brief 垂直半径
    lv_coord_t ry;
    /// @brief 绘制模式
    urq_canvas_draw_mode_t draw_mode;
    /// @brief 填充颜色
    urq_color_ref_t fill_color;
    /// @brief 填充透明度
    uint8_t fill_opacity;
    /// @brief 边框颜色
    urq_color_ref_t border_color;
    /// @brief 边框透明度
    uint8_t border_opacity;
    /// @brief 边框宽度
    uint8_t border_width;
} urq_canvas_ellipse_t;

/// @brief 弧形配置
typedef struct {
    /// @brief 中心点
    lv_point_t center;
    /// @brief 半径
    lv_coord_t radius;
    /// @brief 起始角度 (0-360度)
    int16_t start_angle;
    /// @brief 结束角度 (0-360度)
    int16_t end_angle;
    /// @brief 线宽
    uint8_t width;
    /// @brief 颜色
    urq_color_ref_t color;
    /// @brief 透明度
    uint8_t opacity;
    /// @brief 圆角
    bool rounded;
} urq_canvas_arc_t;

/// @brief 扇形配置
typedef struct {
    /// @brief 中心点
    lv_point_t center;
    /// @brief 半径
    lv_coord_t radius;
    /// @brief 起始角度 (0-360度)
    int16_t start_angle;
    /// @brief 结束角度 (0-360度)
    int16_t end_angle;
    /// @brief 绘制模式
    urq_canvas_draw_mode_t draw_mode;
    /// @brief 填充颜色
    urq_color_ref_t fill_color;
    /// @brief 填充透明度
    uint8_t fill_opacity;
    /// @brief 边框颜色
    urq_color_ref_t border_color;
    /// @brief 边框透明度
    uint8_t border_opacity;
    /// @brief 边框宽度
    uint8_t border_width;
} urq_canvas_sector_t;

/// @brief 扇环配置
typedef struct {
    /// @brief 中心点
    lv_point_t center;
    /// @brief 内半径
    lv_coord_t inner_radius;
    /// @brief 外半径
    lv_coord_t outer_radius;
    /// @brief 起始角度 (0-360度)
    int16_t start_angle;
    /// @brief 结束角度 (0-360度)
    int16_t end_angle;
    /// @brief 绘制模式
    urq_canvas_draw_mode_t draw_mode;
    /// @brief 填充颜色
    urq_color_ref_t fill_color;
    /// @brief 填充透明度
    uint8_t fill_opacity;
    /// @brief 边框颜色
    urq_color_ref_t border_color;
    /// @brief 边框透明度
    uint8_t border_opacity;
    /// @brief 边框宽度
    uint8_t border_width;
} urq_canvas_ring_t;

/// @brief Canvas图形配置联合体
typedef struct {
    /// @brief 图形类型
    urq_canvas_shape_type_t type;
    /// @brief 图形配置
    union {
        urq_canvas_line_t line;
        urq_canvas_rectangle_t rectangle;
        urq_canvas_polygon_t polygon;
        urq_canvas_ellipse_t ellipse;
        urq_canvas_arc_t arc;
        urq_canvas_sector_t sector;
        urq_canvas_ring_t ring;
    } shape;
} urq_canvas_shape_t;

/// @brief Canvas组件配置
typedef struct {
    /// @brief Canvas宽度
    lv_coord_t canvas_width;
    /// @brief Canvas高度
    lv_coord_t canvas_height;
    /// @brief 背景颜色
    urq_color_ref_t bg_color;
    /// @brief 背景透明度
    uint8_t bg_opacity;
    /// @brief 图形数组
    urq_canvas_shape_t *shapes;
    /// @brief 图形数量
    size_t shape_count;
} urq_canvas_widget_conf_t;

/// @brief 初始化Canvas组件配置
static inline void urq_canvas_widget_conf_init_inplace(
    urq_canvas_widget_conf_t *self) __attribute__((__nonnull__(1)));

/// @brief 释放Canvas组件配置
static inline void urq_canvas_widget_conf_free_inplace(
    urq_canvas_widget_conf_t *self) __attribute__((__nonnull__(1)));

// ================= impl =============================

void urq_canvas_widget_conf_init_inplace(urq_canvas_widget_conf_t *self)
{
    self->canvas_width = 200;
    self->canvas_height = 150;
    urq_color_ref_init_inplace(&self->bg_color);
    self->bg_opacity = 255;
    self->shapes = NULL;
    self->shape_count = 0;
}

void urq_canvas_widget_conf_free_inplace(urq_canvas_widget_conf_t *self)
{
    if (self->shapes != NULL) {
        // 释放多边形的顶点数组
        for (size_t i = 0; i < self->shape_count; i++) {
            if (self->shapes[i].type == URQ_CANVAS_SHAPE_POLYGON &&
                self->shapes[i].shape.polygon.points != NULL) {
                urq_free(self->shapes[i].shape.polygon.points);
            }
        }
        urq_free(self->shapes);
        self->shapes = NULL;
    }
    self->shape_count = 0;
}

#ifdef __cplusplus
}
#endif

#endif // URQ__CANVAS__CONF_H
