#pragma once

#include "lvgl.h"
#include "urq/color.h"
#include "urq/marquee.h"
#include "urq/position_offset.h"
#include "urq/preload.h"

#include <limits.h>
#include <stdint.h>
#include <stdio.h>

#ifndef URQ__FONT_H
#define URQ__FONT_H
#ifdef __cplusplus
extern "C" {
#endif

typedef struct {
    /// @brief [optional]文本边距
    urq_position_offset_t *text_padding;
    /// @brief [optional]跑马灯效果
    urq_marquee_t *marquee;
    /// @brief [optional]字体颜色
    urq_color_ref_t color;
    /// @brief [optional]文本对齐方式
    lv_align_t text_align;
    /// @brief [optional]字体ID
    uint16_t id;
    /// @brief [optional]闪烁间隔
    uint8_t blink_interval;
} urq_font_t;

static inline void urq_font_init_inplace(urq_font_t *self)
    __attribute__((__nonnull__(1)));

static inline void urq_font_free_inplace(urq_font_t *self)
    __attribute__((__nonnull__(1)));

// impl
//

void urq_font_init_inplace(urq_font_t *self)
{
    self->id = 0;
    self->text_padding = NULL;
    self->marquee = NULL;
    self->text_align = LV_ALIGN_CENTER;
    urq_color_ref_init_inplace(&self->color);
    self->blink_interval = 0;
}

void urq_font_free_inplace(urq_font_t *self)
{
    if (self->text_padding != NULL) {
        urq_position_offset_free_inplace(self->text_padding);
        urq_free(self->text_padding);
        self->text_padding = NULL;
    }

    if (self->marquee != NULL) {
        urq_marquee_free(self->marquee);
        urq_free(self->marquee);
        self->marquee = NULL;
    }
}

#ifdef __cplusplus
}
#endif
#endif // URQ__FONT_H
