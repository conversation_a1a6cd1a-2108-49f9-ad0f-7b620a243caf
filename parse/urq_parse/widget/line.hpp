#pragma once

#include "urq/line/conf.h"
#include "urq_parse/Context.hpp"
#include "urq_parse/common/set_color.hpp"
#include "urq_parse/common/set_data.hpp"
#include "urq_parse/common/set_line.hpp"
#include "urq_parse/common/set_scale.hpp"
#include "urq_parse/util.hpp"
#include <stdint.h>

#ifndef URQ_PARSE__WIDGET__LINE_HPP
#define URQ_PARSE__WIDGET__LINE_HPP

namespace urq::parse::widget {

/// @brief 刻度尺组件解析
static inline int set_line(
    Context &ctx, urq_line_widget_conf_t *&ptr,
    const znd::project::v1::Widget &proto) noexcept
{
    ptr = malloc<urq_line_widget_conf_t>();
    if (ptr == nullptr) {
        return -1;
    }
    urq_line_widget_conf_init_inplace(ptr);

    ctx.set_conf((urq_widget_conf_t *)ptr);
    urq_used(proto);

#if 0
    auto &line = proto.widget_line();

    // 设置刻度尺类型
    ptr->type = (urq_line_type_t)line.line_type();

    // 解析刻度配置
    if (line.has_scale_config()) {
        common::set_scale(ctx, ptr->scale, line.scale_config());
    }

    // 解析主线配置
    if (line.has_main_line()) {
        common::set_line(ctx, ptr->main_line, line.main_line());
    }

    ptr->radius = (lv_coord_t)line.radius_u16();

    if (line.points_size() > 1) {
        ptr->point = malloc<lv_point_t>((size_t)line.points_size());
        for (int i = 0; i < line.points_size(); i++) {
            ptr->point[i].x = (lv_coord_t)line.points(i).x_i16();
            ptr->point[i].y = (lv_coord_t)line.points(i).y_i16();
        }
        ptr->point_cnt = (uint8_t)line.points_size();
    }

#endif

    return 0;
}

} // namespace urq::parse::widget

#endif // #ifndef URQ_PARSE__WIDGET__LINE_HPP
