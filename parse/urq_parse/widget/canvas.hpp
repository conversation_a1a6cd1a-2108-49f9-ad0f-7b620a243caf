#pragma once

#include "urq/canvas/conf.h"
#include "urq_parse/Context.hpp"
#include "urq_parse/common/set_color.hpp"
#include "urq_parse/util.hpp"
#include <stdint.h>

#ifndef URQ_PARSE__WIDGET__CANVAS_HPP
#define URQ_PARSE__WIDGET__CANVAS_HPP

namespace urq::parse::widget {

/// @brief Canvas组件解析
static inline int set_canvas(
    Context &ctx, urq_canvas_widget_conf_t *&ptr,
    const znd::project::v1::Widget &proto) noexcept
{
    ptr = malloc<urq_canvas_widget_conf_t>();
    if (ptr == nullptr) {
        return -1;
    }
    urq_canvas_widget_conf_init_inplace(ptr);

    ctx.set_conf((urq_widget_conf_t *)ptr);
    urq_used(proto);

    // TODO: 解析Canvas配置
    // 这里可以添加从protobuf解析Canvas配置的代码
    // 例如：
    // auto &canvas = proto.widget_canvas();
    // ptr->canvas_width = canvas.width();
    // ptr->canvas_height = canvas.height();
    // common::set_color(ctx, ptr->bg_color, canvas.bg_color());
    // ptr->bg_opacity = canvas.bg_opacity();
    
    // 解析图形列表
    // if (canvas.shapes_size() > 0) {
    //     ptr->shape_count = canvas.shapes_size();
    //     ptr->shapes = malloc<urq_canvas_shape_t>(ptr->shape_count);
    //     for (size_t i = 0; i < ptr->shape_count; i++) {
    //         // 解析每个图形
    //     }
    // }

    return 0;
}

} // namespace urq::parse::widget

#endif // #ifndef URQ_PARSE__WIDGET__CANVAS_HPP
