// 刻度尺组件使用示例
import { posOf, sizeOf, type FullWidget } from "../../helper/utils";
import * as widget from "../../../proto/dev/widget";

// 创建水平刻度尺的工厂函数
export function createHorizontalRuler(
    id: number,
    x: number,
    y: number,
    width: number,
    height: number,
    minValue: number,
    maxValue: number,
): FullWidget {
    return {
        ui: {
            idU16: id,
            location: posOf(x, y),
            size: sizeOf(width, height),
            widget: {
                $case: "widgetRuler",
                value: {
                    rulerType: widget.WidgetRuler_RulerType.RULER_TYPE_HORIZONTAL,
                    direction: widget.WidgetRuler_RulerDirection.RULER_DIRECTION_BOTTOM_RIGHT,
                    points: [{xI16: 20, yI16: 20}, {xI16: 140, yI16: 60}],
                    scaleConfig: {
                        show: true,
                        scaleShowType: widget.ScaleValueConfig_ScaleShowType.SCALE_SHOW_TYPE_POINT,
                        gridShowType: widget.ScaleValueConfig_GridShowType.GRID_SHOW_TYPE_MAIN,
                        scaleMain: {
                            scaleCountU8: 10,
                            scaleWidthU8: 2,
                            scaleDrawLen: 15,
                            color: {
                                color: {
                                    $case: "colorValueU32",
                                    value: 0x000000ff,
                                },
                            },
                        },
                        scaleSec: {
                            scaleCountU8: 5,
                            scaleWidthU8: 2,
                            scaleDrawLen: 8,
                            color: {
                                color: {
                                    $case: "colorValueU32",
                                    value: 0x808080ff,
                                },
                            },
                        },
                        minValue: {
                            from: {
                                $case: "valueI32",
                                value: minValue,
                            },
                        },
                        maxValue: {
                            from: {
                                $case: "valueI32",
                                value: maxValue,
                            },
                        },
                    },
                    mainLine: {
                        lineType: widget.LineType.LINE_TYPE_LINE,
                        widthU8: 8,
                        color: {
                            color: {
                                $case: "colorValueU32",
                                value: 0x000000ff,
                            },
                        },
                    },
                },
            },
        },
        attributes: {},
        actions: [],
    };
}

// 创建垂直刻度尺的工厂函数（适用于量杯等）
export function createVerticalRuler(
    id: number,
    x: number,
    y: number,
    width: number,
    height: number,
    minValue: number,
    maxValue: number,
    currentValue?: number
): FullWidget {
    return {
        ui: {
            idU16: id,
            location: posOf(x, y),
            size: sizeOf(width, height),
            widget: {
                $case: "widgetRuler",
                value: {
                    rulerType: widget.WidgetRuler_RulerType.RULER_TYPE_VERTICAL,
                    direction: widget.WidgetRuler_RulerDirection.RULER_DIRECTION_TOP_LEFT,
                    points: [{xI16: 240, yI16: 80}, {xI16: 240, yI16: 20}],
                    scaleConfig: {
                        show: true,
                        scaleShowType: widget.ScaleValueConfig_ScaleShowType.SCALE_SHOW_TYPE_POINT,
                        scaleMain: {
                            scaleCountU8: 10,
                            scaleWidthU8: 2,
                            scaleDrawLen: 20,
                            color: {
                                color: {
                                    $case: "colorValueU32",
                                    value: 0x000000ff,
                                },
                            },
                        },
                        scaleSec: {
                            scaleCountU8: 20,
                            scaleWidthU8: 1,
                            scaleDrawLen: 10,
                            color: {
                                color: {
                                    $case: "colorValueU32",
                                    value: 0x808080ff,
                                },
                            },
                        },
                        minValue: {
                            from: {
                                $case: "valueI32",
                                value: minValue,
                            },
                        },
                        maxValue: {
                            from: {
                                $case: "valueI32",
                                value: maxValue,
                            },
                        },
                    },
                    mainLine: {
                        lineType: widget.LineType.LINE_TYPE_LINE,
                        widthU8: 3,
                        color: {
                            color: {
                                $case: "colorValueU32",
                                value: 0x0064c8ff,
                            },
                        },
                    },
                    currentValue: currentValue !== undefined ? {
                        from: {
                            $case: "valueI32",
                            value: currentValue,
                        },
                    } : undefined,
                },
            },
        },
        attributes: {},
        actions: [],
    };
}

// 创建弧形刻度尺的工厂函数（适用于仪表盘等）
export function createArcRuler(
    id: number,
    x: number,
    y: number,
    size: number,
    minValue: number,
    maxValue: number,
    currentValue?: number
): FullWidget {
    return {
        ui: {
            idU16: id,
            location: posOf(x, y),
            size: sizeOf(size, size),
            widget: {
                $case: "widgetRuler",
                value: {
                    rulerType: widget.WidgetRuler_RulerType.RULER_TYPE_ARC,
                    direction: widget.WidgetRuler_RulerDirection.RULER_DIRECTION_TOP_LEFT,
                    scaleConfig: {
                        show: true,
                        scaleShowType: widget.ScaleValueConfig_ScaleShowType.SCALE_SHOW_TYPE_POINT,
                        gridShowType: widget.ScaleValueConfig_GridShowType.GRID_SHOW_TYPE_MAIN,
                        scaleMain: {
                            scaleCountU8: 12,
                            scaleWidthU8: 3,
                            scaleDrawLen: 20,
                            color: {
                                color: {
                                    $case: "colorValueU32",
                                    value: 0x000000ff,
                                },
                            },
                        },
                        scaleSec: {
                            scaleCountU8: 60,
                            scaleWidthU8: 1,
                            scaleDrawLen: 10,
                            color: {
                                color: {
                                    $case: "colorValueU32",
                                    value: 0x646464ff,
                                },
                            },
                        },
                        scaleRadiusU8: Math.floor(size * 0.4),
                        minValue: {
                            from: {
                                $case: "valueI32",
                                value: minValue,
                            },
                        },
                        maxValue: {
                            from: {
                                $case: "valueI32",
                                value: maxValue,
                            },
                        },
                    },
                    mainLine: {
                        lineType: widget.LineType.LINE_TYPE_LINE,
                        widthU8: 4,
                        color: {
                            color: {
                                $case: "colorValueU32",
                                value: 0x323232ff,
                            },
                        },
                    },
                    radiusU16: Math.floor(size * 0.4),
                    currentValue: currentValue !== undefined ? {
                        from: {
                            $case: "valueI32",
                            value: currentValue,
                        },
                    } : undefined,
                },
            },
        },
        attributes: {},
        actions: [],
    };
}

// 使用示例
export const rulerExamples = {
    // 温度计刻度尺
    thermometer: createVerticalRuler(3001, 100, 50, 40, 200, -10, 50, 25),
    
    // 压力表刻度尺
    // pressureGauge: createArcRuler(3002, 300, 100, 150, 0, 10, 135, 405),
    
    // 长度测量尺
    lengthRuler: createHorizontalRuler(3003, 50, 300, 400, 30, 0, 50),
    
    // 液体容量刻度
    liquidMeasure: createVerticalRuler(3004, 500, 50, 50, 250, 0, 1000),
};
